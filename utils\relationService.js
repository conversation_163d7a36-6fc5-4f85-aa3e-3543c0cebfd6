// 关系服务模块 - 处理产品-区域-讲解点-音频的三层级API
const httpService = require('./httpService');

class RelationService {
  constructor() {
    this.cacheKeyPrefix = 'relation_cache_';
    this.cacheTime = 5 * 60 * 1000; // 5分钟缓存
  }

  // 获取产品区域关系详情
  async getProductAreaDetails(productId) {
    try {
      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('product_area', { productId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的产品区域关系数据');
        return cachedData;
      }

      console.log(`从服务器获取产品区域关系 (产品ID: ${productId})`);
      const response = await httpService.get(`/api/relations/product-area/product/${productId}/details`, {}, {
        loadingText: '加载区域信息中...'
      });

      // 处理返回的数据结构
      let areaList = [];
      if (response && Array.isArray(response)) {
        areaList = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        areaList = response.data;
      } else if (response && response.list && Array.isArray(response.list)) {
        areaList = response.list;
      }

      // 按sortOrder排序
      areaList.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

      // 缓存数据
      this.setCache(cacheKey, areaList);

      console.log('产品区域关系获取成功，区域数量:', areaList.length);
      return areaList;
    } catch (error) {
      console.error('获取产品区域关系失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取区域讲解点关系详情
  async getAreaPointDetails(areaId) {
    try {
      if (!areaId) {
        throw new Error('区域ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('area_point', { areaId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的区域讲解点关系数据');
        return cachedData;
      }

      console.log(`从服务器获取区域讲解点关系 (区域ID: ${areaId})`);
      const response = await httpService.get(`/api/relations/area-point/area/${areaId}/details`, {}, {
        loadingText: '加载讲解点中...'
      });

      // 处理返回的数据结构
      let pointList = [];
      if (response && Array.isArray(response)) {
        pointList = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        pointList = response.data;
      } else if (response && response.list && Array.isArray(response.list)) {
        pointList = response.list;
      }

      // 按sortOrder排序
      pointList.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

      // 缓存数据
      this.setCache(cacheKey, pointList);

      console.log('区域讲解点关系获取成功，讲解点数量:', pointList.length);
      return pointList;
    } catch (error) {
      console.error('获取区域讲解点关系失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取讲解点音频关系详情
  async getPointAudioDetails(pointId) {
    try {
      if (!pointId) {
        throw new Error('讲解点ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('point_audio', { pointId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的讲解点音频关系数据');
        return cachedData;
      }

      console.log(`从服务器获取讲解点音频关系 (讲解点ID: ${pointId})`);
      const response = await httpService.get(`/api/relations/point-audio/point/${pointId}/details`, {}, {
        loadingText: '加载音频信息中...'
      });

      // 处理返回的数据结构
      let audioList = [];
      if (response && Array.isArray(response)) {
        audioList = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        audioList = response.data;
      } else if (response && response.list && Array.isArray(response.list)) {
        audioList = response.list;
      }

      // 按sortOrder排序
      audioList.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

      // 缓存数据
      this.setCache(cacheKey, audioList);

      console.log('讲解点音频关系获取成功，音频数量:', audioList.length);
      return audioList;
    } catch (error) {
      console.error('获取讲解点音频关系失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取完整的三层级数据（产品 -> 区域 -> 讲解点 -> 音频）
  async getCompleteHierarchyData(productId) {
    try {
      console.log('开始获取完整的三层级数据:', productId);

      // 1. 获取产品区域关系
      const areas = await this.getProductAreaDetails(productId);
      
      if (!areas || areas.length === 0) {
        console.log('没有找到相关区域');
        return { areas: [], selectedArea: null, audioData: [] };
      }

      // 2. 默认选中第一个区域（sortOrder最小的）
      const selectedArea = areas[0];
      
      // 3. 获取选中区域的讲解点和音频数据
      const audioData = await this.getAreaAudioData(selectedArea.areaId);

      return {
        areas: areas,
        selectedArea: selectedArea,
        audioData: audioData
      };
    } catch (error) {
      console.error('获取完整三层级数据失败:', error);
      throw error;
    }
  }

  // 获取指定区域的音频数据（区域 -> 讲解点 -> 音频）
  async getAreaAudioData(areaId) {
    try {
      console.log('获取区域音频数据:', areaId);

      // 1. 获取区域的讲解点
      const points = await this.getAreaPointDetails(areaId);
      
      if (!points || points.length === 0) {
        console.log('该区域没有讲解点');
        return [];
      }

      // 2. 为每个讲解点获取音频数据
      const audioDataPromises = points.map(async (point) => {
        try {
          const audioList = await this.getPointAudioDetails(point.pointId);
          return {
            point: point,
            audioList: audioList
          };
        } catch (error) {
          console.error(`获取讲解点 ${point.pointId} 的音频失败:`, error);
          return {
            point: point,
            audioList: []
          };
        }
      });

      const audioData = await Promise.all(audioDataPromises);
      
      console.log('区域音频数据获取完成，讲解点数量:', audioData.length);
      return audioData;
    } catch (error) {
      console.error('获取区域音频数据失败:', error);
      throw error;
    }
  }

  // 缓存相关方法
  buildCacheKey(type, params) {
    const paramsStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramsStr}`;
  }

  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取关系缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置关系缓存失败:', error);
    }
  }

  clearCache(pattern = null) {
    try {
      const storage = wx.getStorageInfoSync();
      storage.keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          if (!pattern || key.includes(pattern)) {
            wx.removeStorageSync(key);
          }
        }
      });
      console.log('关系缓存已清除');
    } catch (error) {
      console.error('清除关系缓存失败:', error);
    }
  }
}

// 创建单例实例
const relationService = new RelationService();

module.exports = relationService;
