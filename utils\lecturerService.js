// 讲师服务模块
const httpService = require('./httpService');

class LecturerService {
  constructor() {
    this.cacheKeyPrefix = 'lecturer_cache_';
    this.cacheTime = 5 * 60 * 1000; // 5分钟缓存
  }

  // 根据讲师ID获取讲师详情
  async getLecturerDetail(lecturerId) {
    try {
      if (!lecturerId) {
        throw new Error('讲师ID不能为空');
      }

      // 构建缓存key
      const cacheKey = this.buildCacheKey('detail', { lecturerId });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的讲师详情数据');
        return cachedData;
      }

      console.log(`从服务器获取讲师详情 (讲师ID: ${lecturerId})`);
      const lecturerDetail = await httpService.get(`/api/lecturers/${lecturerId}`, {}, {
        loadingText: '加载讲师信息中...'
      });

      // 处理返回的数据结构
      let detailData = null;
      if (lecturerDetail && lecturerDetail.data) {
        detailData = lecturerDetail.data;
      } else if (lecturerDetail) {
        detailData = lecturerDetail;
      }

      if (!detailData) {
        throw new Error('讲师详情数据为空');
      }

      // 缓存数据
      this.setCache(cacheKey, detailData);

      console.log('讲师详情获取成功:', detailData.name || detailData.lecturerId);
      return detailData;
    } catch (error) {
      console.error('获取讲师详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取讲师列表
  async getLecturerList(params = {}) {
    try {
      const {
        current = 1,
        size = 20,
        status = '1'
      } = params;

      // 构建缓存key
      const cacheKey = this.buildCacheKey('list', { current, size, status });

      // 检查缓存
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        console.log('使用缓存的讲师列表数据');
        return cachedData;
      }

      console.log('从服务器获取讲师列表');
      const queryParams = {
        current: current.toString(),
        size: size.toString(),
        status: status
      };

      const lecturers = await httpService.get('/api/lecturers/page', queryParams, {
        loadingText: '加载讲师列表中...'
      });

      // 处理返回的数据结构
      let listData = { list: [], total: 0, current: 1, size: 20 };

      if (lecturers) {
        if (lecturers.records && Array.isArray(lecturers.records)) {
          // 标准分页数据结构
          listData = {
            list: lecturers.records,
            total: lecturers.total || lecturers.records.length,
            current: lecturers.current || current,
            size: lecturers.size || size,
            pages: lecturers.pages || 1
          };
        } else if (Array.isArray(lecturers)) {
          // 直接数组结构
          listData = {
            list: lecturers,
            total: lecturers.length,
            current: current,
            size: size,
            pages: 1
          };
        }
      }

      // 缓存数据
      this.setCache(cacheKey, listData);

      console.log('讲师列表获取成功，数量:', listData.list.length);
      return listData;
    } catch (error) {
      console.error('获取讲师列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 缓存相关方法
  buildCacheKey(type, params) {
    const paramsStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    return `${this.cacheKeyPrefix}${type}_${paramsStr}`;
  }

  getCache(cacheKey) {
    try {
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取讲师缓存失败:', error);
    }
    return null;
  }

  setCache(cacheKey, data) {
    try {
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置讲师缓存失败:', error);
    }
  }

  clearCache(pattern = null) {
    try {
      const storage = wx.getStorageInfoSync();
      storage.keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          if (!pattern || key.includes(pattern)) {
            wx.removeStorageSync(key);
          }
        }
      });
      console.log('讲师缓存已清除');
    } catch (error) {
      console.error('清除讲师缓存失败:', error);
    }
  }

  // 验证讲师数据
  validateLecturer(lecturer) {
    if (!lecturer) {
      return false;
    }

    const requiredFields = ['id', 'name'];
    return requiredFields.every(field => lecturer.hasOwnProperty(field) && lecturer[field]);
  }

  // 格式化讲师数据用于显示
  formatLecturerForDisplay(lecturer) {
    if (!this.validateLecturer(lecturer)) {
      return null;
    }

    return {
      id: lecturer.id,
      lecturerId: lecturer.lecturer_id || lecturer.id,
      name: lecturer.name,
      introduction: lecturer.introduction || lecturer.intro || '',
      avatarUrl: lecturer.avatar_url || lecturer.avatarUrl || '',
      experience: lecturer.experience || '',
      rating: lecturer.rating || 0,
      status: lecturer.status || 1,
      createdAt: lecturer.created_at || lecturer.createdAt,
      updatedAt: lecturer.updated_at || lecturer.updatedAt
    };
  }
}

// 创建单例实例
const lecturerService = new LecturerService();

module.exports = lecturerService;
