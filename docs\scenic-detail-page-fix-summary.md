# 景区详情页面参数获取修复总结

## 问题描述

景区详情页面虽然能够接收到跳转参数，但是无法正确获取和处理这些参数，导致API调用失败。

### 问题现象
```
页面参数: {}  // 参数为空对象
跳转到景区详情页面: {scenicId: "scenic_1749304543166_xsxcmn", title: "苏州博物馆|细品苏式风雅贝氏美学"}
```

### 问题原因
1. **配置问题**: 页面被配置为组件（`"component": true`）
2. **参数获取方式错误**: 组件无法通过 `getCurrentPages()` 正确获取页面参数
3. **生命周期不匹配**: 使用了组件的生命周期而非页面生命周期

## 解决方案

### 1. 修改页面配置

**文件**: `pages/lanhu_dulijingqu/component.json`

**修改前**:
```json
{
  "component": true,
  "usingComponents": {},
  "navigationBarTitleText": "景区详情"
}
```

**修改后**:
```json
{
  "usingComponents": {},
  "navigationBarTitleText": "景区详情"
}
```

**说明**: 移除 `"component": true` 配置，将其改为页面模式。

### 2. 修改页面结构

**文件**: `pages/lanhu_dulijingqu/component.js`

#### 2.1 从组件改为页面
**修改前**:
```javascript
Component({
  properties: {},
  data: { ... },
  lifetimes: {
    created: function () { ... },
    attached: function () { ... },
    detached: function () { ... },
  },
  methods: { ... }
});
```

**修改后**:
```javascript
Page({
  data: { ... },
  
  onLoad: function(options) {
    console.log('景区详情页面加载');
    console.log('页面参数:', options);
    this.initPage(options);
  },

  onShow: function() { ... },
  onHide: function() { ... },
  onUnload: function() { ... },
  
  // 页面方法直接定义，不需要 methods 包装
  initPage: function(options) { ... }
});
```

#### 2.2 修改参数获取方式
**修改前**:
```javascript
initPage: function() {
  // 错误的参数获取方式
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};
}
```

**修改后**:
```javascript
onLoad: function(options) {
  // 直接从 onLoad 获取参数
  this.initPage(options);
},

initPage: function(options) {
  // 直接使用传入的参数
  const scenicId = options.scenicId;
}
```

### 3. 生命周期对应关系

| 组件生命周期 | 页面生命周期 | 说明 |
|-------------|-------------|------|
| `created` | - | 页面无对应生命周期 |
| `attached` | `onLoad` | 页面加载时 |
| `ready` | `onReady` | 页面初次渲染完成 |
| `moved` | - | 页面无对应生命周期 |
| `detached` | `onUnload` | 页面卸载时 |
| - | `onShow` | 页面显示时 |
| - | `onHide` | 页面隐藏时 |

## 修复效果

### 修复前
```
页面参数: {}
景区ID参数缺失
```

### 修复后
```
景区详情页面加载
页面参数: {scenicId: "scenic_1749304543166_xsxcmn"}
初始化页面，参数: {scenicId: "scenic_1749304543166_xsxcmn"}
开始加载景区详情: scenic_1749304543166_xsxcmn
```

## 技术要点

### 1. 微信小程序页面 vs 组件

**页面特点**:
- 可以通过 `onLoad(options)` 直接获取页面参数
- 有完整的页面生命周期
- 可以设置页面标题等页面级配置
- 适合作为独立的功能页面

**组件特点**:
- 通过 `properties` 接收父组件传递的数据
- 有组件特定的生命周期
- 适合作为可复用的功能模块
- 无法直接获取页面参数

### 2. 参数传递流程

1. **首页跳转**:
   ```javascript
   wx.navigateTo({
     url: `/pages/lanhu_dulijingqu/component?scenicId=${actualScenicId}`
   });
   ```

2. **详情页面接收**:
   ```javascript
   onLoad: function(options) {
     // options = {scenicId: "scenic_1749304543166_xsxcmn"}
     this.initPage(options);
   }
   ```

3. **参数处理**:
   ```javascript
   initPage: function(options) {
     const scenicId = options.scenicId;
     this.loadScenicDetail(scenicId);
   }
   ```

### 3. 错误处理增强

```javascript
initPage: function(options) {
  console.log('初始化页面，参数:', options);
  
  const scenicId = options.scenicId;
  if (!scenicId) {
    console.error('景区ID参数缺失');
    // 显示错误状态
    return;
  }
  
  // 继续处理
  this.loadScenicDetail(scenicId);
}
```

## 验证方法

### 1. 控制台日志验证
检查以下日志是否正常输出：
```
景区详情页面加载
页面参数: {scenicId: "xxx"}
初始化页面，参数: {scenicId: "xxx"}
开始加载景区详情: xxx
```

### 2. 功能验证
- 点击首页景区卡片
- 确认页面正常跳转
- 确认参数正确传递
- 确认API正常调用
- 确认数据正常显示

## 注意事项

### 1. 文件命名
虽然文件名为 `component.js`，但实际上是页面文件。建议保持现有命名以避免影响其他引用。

### 2. 路由配置
确保在 `app.json` 中正确配置了页面路径：
```json
{
  "pages": [
    "pages/lanhu_dulijingqu/component"
  ]
}
```

### 3. 样式兼容
页面模式下的样式与组件模式基本相同，无需特殊处理。

## 总结

本次修复主要解决了：

1. ✅ **配置修复**: 将组件配置改为页面配置
2. ✅ **结构调整**: 从 Component 改为 Page
3. ✅ **参数获取**: 使用正确的页面参数获取方式
4. ✅ **生命周期**: 使用页面生命周期替代组件生命周期
5. ✅ **错误处理**: 增强参数验证和错误提示

修复后，景区详情页面可以正确获取跳转参数并调用API加载数据。
