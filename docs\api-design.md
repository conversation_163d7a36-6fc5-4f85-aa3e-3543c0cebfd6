# 旅游讲解小程序 API 接口设计

## 1. 省份相关接口

### 1.1 获取省份列表
```
GET /api/provinces
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "江苏",
      "code": "JS",
      "sort": 1,
      "status": 1
    },
    {
      "id": 2,
      "name": "浙江",
      "code": "ZJ",
      "sort": 2,
      "status": 1
    }
  ]
}
```

## 2. 城市相关接口

### 2.1 根据省份获取城市列表
```
GET /api/cities?province_id={province_id}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "南京",
      "province_id": 1,
      "code": "NJ",
      "sort": 1,
      "status": 1
    },
    {
      "id": 2,
      "name": "苏州",
      "province_id": 1,
      "code": "SZ",
      "sort": 2,
      "status": 1
    }
  ]
}
```

## 3. 轮播图接口

### 3.1 获取轮播图数据
```
GET /api/carousels?province_id={province_id}&type=home
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "苏州园林",
      "subtitle": "江南水乡的诗意栖居",
      "image": "https://example.com/images/carousel1.jpg",
      "scenic_id": "suzhou_garden",
      "province_id": 1,
      "sort": 1,
      "status": 1
    }
  ]
}
```

## 4. 景区相关接口

### 4.1 获取推荐景区列表
```
GET /api/scenics/recommend?province_id={province_id}&city_id={city_id}&page=1&limit=10
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "scenic_id": "suzhou_museum",
        "title": "苏州博物馆|细品苏式风雅贝氏美学",
        "subtitle": "走进苏州博物馆，从古典园林到吴地文化知无不言",
        "description": "2小时讲解时长丨建筑鉴赏丨国宝物语",
        "image": "https://example.com/images/scenic1.jpg",
        "price": 0,
        "rating": 4.8,
        "province_id": 1,
        "city_id": 2,
        "status": 1
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10
  }
}
```

### 4.2 获取景区详情
```
GET /api/scenics/{scenic_id}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "scenic_id": "suzhou_museum",
    "title": "苏州博物馆",
    "description": "详细介绍...",
    "images": [
      "https://example.com/images/scenic1_1.jpg",
      "https://example.com/images/scenic1_2.jpg"
    ],
    "price": 0,
    "rating": 4.8,
    "open_time": "09:00-17:00",
    "address": "苏州市姑苏区东北街204号",
    "phone": "0512-67575666",
    "province_id": 1,
    "city_id": 2,
    "latitude": 31.3056,
    "longitude": 120.6194,
    "status": 1
  }
}
```

## 5. 用户相关接口

### 5.1 微信登录
```
POST /api/auth/wechat/login
```

**请求参数：**
```json
{
  "code": "wx_login_code",
  "encrypted_data": "encrypted_user_info",
  "iv": "initialization_vector"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "jwt_token_string",
    "user_info": {
      "id": 1,
      "openid": "wx_openid",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg",
      "phone": "13800138000",
      "province": "江苏",
      "city": "苏州",
      "created_at": "2024-01-01 00:00:00"
    }
  }
}
```

### 5.2 获取用户信息
```
GET /api/user/info
Authorization: Bearer {token}
```

### 5.3 更新用户信息
```
PUT /api/user/info
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "nickname": "新昵称",
  "avatar": "https://example.com/new_avatar.jpg",
  "phone": "13800138001"
}
```

## 6. 数据库表结构设计

### 6.1 省份表 (provinces)
```sql
CREATE TABLE provinces (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL COMMENT '省份名称',
  code VARCHAR(10) NOT NULL COMMENT '省份代码',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6.2 城市表 (cities)
```sql
CREATE TABLE cities (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL COMMENT '城市名称',
  province_id INT NOT NULL COMMENT '省份ID',
  code VARCHAR(10) NOT NULL COMMENT '城市代码',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (province_id) REFERENCES provinces(id)
);
```

### 6.3 景区表 (scenics)
```sql
CREATE TABLE scenics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  scenic_id VARCHAR(100) UNIQUE NOT NULL COMMENT '景区唯一标识',
  title VARCHAR(200) NOT NULL COMMENT '景区标题',
  subtitle VARCHAR(500) COMMENT '景区副标题',
  description TEXT COMMENT '景区描述',
  image VARCHAR(500) COMMENT '主图片',
  images JSON COMMENT '图片集合',
  price DECIMAL(10,2) DEFAULT 0 COMMENT '价格',
  rating DECIMAL(3,2) DEFAULT 0 COMMENT '评分',
  open_time VARCHAR(100) COMMENT '开放时间',
  address VARCHAR(500) COMMENT '地址',
  phone VARCHAR(20) COMMENT '联系电话',
  province_id INT NOT NULL COMMENT '省份ID',
  city_id INT NOT NULL COMMENT '城市ID',
  latitude DECIMAL(10,6) COMMENT '纬度',
  longitude DECIMAL(10,6) COMMENT '经度',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (province_id) REFERENCES provinces(id),
  FOREIGN KEY (city_id) REFERENCES cities(id)
);
```

### 6.4 轮播图表 (carousels)
```sql
CREATE TABLE carousels (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL COMMENT '标题',
  subtitle VARCHAR(500) COMMENT '副标题',
  image VARCHAR(500) NOT NULL COMMENT '图片URL',
  scenic_id VARCHAR(100) COMMENT '关联景区ID',
  province_id INT COMMENT '省份ID',
  type VARCHAR(20) DEFAULT 'home' COMMENT '类型：home-首页',
  sort INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (province_id) REFERENCES provinces(id)
);
```

### 6.5 用户表 (users)
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  unionid VARCHAR(100) COMMENT '微信unionid',
  nickname VARCHAR(100) COMMENT '昵称',
  avatar VARCHAR(500) COMMENT '头像',
  phone VARCHAR(20) COMMENT '手机号',
  province VARCHAR(50) COMMENT '省份',
  city VARCHAR(50) COMMENT '城市',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 7. 表关联关系

- provinces (1) -> cities (N)
- provinces (1) -> scenics (N)
- cities (1) -> scenics (N)
- provinces (1) -> carousels (N)
- scenics (1) -> carousels (N)

## 8. 索引建议

```sql
-- 城市表索引
CREATE INDEX idx_cities_province_id ON cities(province_id);
CREATE INDEX idx_cities_status ON cities(status);

-- 景区表索引
CREATE INDEX idx_scenics_province_city ON scenics(province_id, city_id);
CREATE INDEX idx_scenics_status ON scenics(status);
CREATE INDEX idx_scenics_sort ON scenics(sort);

-- 轮播图表索引
CREATE INDEX idx_carousels_province_type ON carousels(province_id, type);
CREATE INDEX idx_carousels_status ON carousels(status);

-- 用户表索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_phone ON users(phone);
```
## 10. 前端数据接口调用示例

### 10.1 小程序端接口调用

```javascript
// utils/api.js - API调用工具类
class ApiService {
  constructor() {
    this.baseUrl = 'https://your-api-domain.com';
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '请求失败'));
          }
        },
        fail: (err) => {
          reject(new Error('网络请求失败'));
        }
      });
    });
  }

  // 获取省份列表
  getProvinces() {
    return this.request({
      url: '/api/provinces'
    });
  }

  // 获取城市列表
  getCities(provinceId) {
    return this.request({
      url: `/api/cities?province_id=${provinceId}`
    });
  }

  // 获取轮播图
  getCarousels(provinceId, type = 'home') {
    return this.request({
      url: `/api/carousels?province_id=${provinceId}&type=${type}`
    });
  }

  // 获取推荐景区
  getRecommendScenics(params) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${params[key]}`)
      .join('&');

    return this.request({
      url: `/api/scenics/recommend?${queryString}`
    });
  }
}

const apiService = new ApiService();
module.exports = apiService;
```

### 10.2 页面中使用API

```javascript
// pages/lanhu_shouye/component.js
const apiService = require('../../utils/api');

Component({
  data: {
    provinces: [],
    cities: [],
    carousels: [],
    scenics: []
  },

  lifetimes: {
    attached: function() {
      this.loadInitialData();
    }
  },

  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        wx.showLoading({ title: '加载中...' });

        // 并行加载省份和轮播图数据
        const [provinces, carousels] = await Promise.all([
          apiService.getProvinces(),
          apiService.getCarousels()
        ]);

        this.setData({
          provinces: provinces.map((item, index) => ({
            ...item,
            selected: index === 0
          })),
          carousels
        });

        // 加载默认省份的城市和景区数据
        if (provinces.length > 0) {
          await this.loadProvinceData(provinces[0].id);
        }

        wx.hideLoading();
      } catch (error) {
        wx.hideLoading();
        wx.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });
      }
    },

    // 加载省份相关数据
    async loadProvinceData(provinceId) {
      try {
        const [cities, carousels, scenics] = await Promise.all([
          apiService.getCities(provinceId),
          apiService.getCarousels(provinceId),
          apiService.getRecommendScenics({
            province_id: provinceId,
            page: 1,
            limit: 10
          })
        ]);

        this.setData({
          cities: [{ id: 0, name: '全部' }, ...cities],
          carousels,
          scenics: scenics.list,
          selectedCityId: 0
        });
      } catch (error) {
        wx.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });
      }
    },

    // 选择城市
    async selectCity(e) {
      const cityId = e.currentTarget.dataset.cityId;
      const provinceId = this.data.provinces[0].id;

      try {
        wx.showLoading({ title: '加载中...' });

        const scenics = await apiService.getRecommendScenics({
          province_id: provinceId,
          city_id: cityId,
          page: 1,
          limit: 10
        });

        this.setData({
          selectedCityId: cityId,
          scenics: scenics.list
        });

        wx.hideLoading();
      } catch (error) {
        wx.hideLoading();
        wx.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        });
      }
    }
  }
});
```
