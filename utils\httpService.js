// HTTP请求服务基类
const config = require('./config');

class HttpService {
  constructor() {
    this.baseUrl = config.api.baseUrl;
    this.timeout = config.api.timeout;
  }

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      // 显示加载状态
      if (options.showLoading !== false) {
        wx.showLoading({
          title: options.loadingText || '加载中...',
          mask: true
        });
      }

      const requestOptions = {
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        timeout: this.timeout,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (options.showLoading !== false) {
            wx.hideLoading();
          }

          if (res.statusCode === 200) {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject(new Error(res.data.message || '请求失败'));
            }
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data.message || '服务器错误'}`));
          }
        },
        fail: (err) => {
          if (options.showLoading !== false) {
            wx.hideLoading();
          }

          let errorMessage = '网络请求失败';
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMessage = '请求超时，请检查网络连接';
            } else if (err.errMsg.includes('fail')) {
              errorMessage = '网络连接失败，请检查网络设置';
            }
          }
          reject(new Error(errorMessage));
        }
      };

      wx.request(requestOptions);
    });
  }

  // GET请求
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      ...options
    });
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  // DELETE请求
  delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  // 带Token的请求
  requestWithAuth(options) {
    const token = wx.getStorageSync('user_token');
    if (!token) {
      return Promise.reject(new Error('未登录，请先登录'));
    }

    return this.request({
      ...options,
      header: {
        'Authorization': `Bearer ${token}`,
        ...options.header
      }
    });
  }

  // 错误处理
  handleError(error, showToast = true) {
    console.error('HTTP请求错误:', error);
    
    if (showToast) {
      wx.showToast({
        title: error.message || '请求失败',
        icon: 'none',
        duration: 2000
      });
    }
    
    return error;
  }
}

// 创建单例实例
const httpService = new HttpService();

module.exports = httpService;
