.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1628rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_1 {
  width: 468rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 258rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.group_2 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.list_1 {
  width: 710rpx;
  height: 898rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 20rpx;
}
.list-items_1-0 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  margin-bottom: 20rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1-0 {
  width: 384rpx;
  height: 44rpx;
  display: flex;
  flex-direction: row;
  margin: 40rpx 0 0 30rpx;
}
.text_2-0 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_2-0 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 24rpx 0 0 30rpx;
}
.text-wrapper_2-0 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_3-0 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.box_3-0 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_4-0 {
  width: 168rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_3-0 {
  background-image: linear-gradient(91deg, rgba(238,116,53,1.000000) 0, rgba(236,87,91,1.000000) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_5-0 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 60rpx;
}
.list-items_1-1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  margin-bottom: 20rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1-1 {
  width: 384rpx;
  height: 44rpx;
  display: flex;
  flex-direction: row;
  margin: 40rpx 0 0 30rpx;
}
.text_2-1 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_2-1 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 24rpx 0 0 30rpx;
}
.text-wrapper_2-1 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_3-1 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.box_3-1 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_4-1 {
  width: 168rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_3-1 {
  background-image: linear-gradient(91deg, rgba(238,116,53,1.000000) 0, rgba(236,87,91,1.000000) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_5-1 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 60rpx;
}
.list-items_1-2 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 286rpx;
  margin-bottom: 20rpx;
  width: 710rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1-2 {
  width: 384rpx;
  height: 44rpx;
  display: flex;
  flex-direction: row;
  margin: 40rpx 0 0 30rpx;
}
.text_2-2 {
  width: 384rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_2-2 {
  width: 442rpx;
  height: 48rpx;
  display: flex;
  flex-direction: row;
  margin: 24rpx 0 0 30rpx;
}
.text-wrapper_2-2 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_3-2 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.box_3-2 {
  width: 650rpx;
  height: 58rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 52rpx 0 20rpx 30rpx;
}
.text_4-2 {
  width: 168rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 2rpx;
}
.text-wrapper_3-2 {
  background-image: linear-gradient(91deg, rgba(238,116,53,1.000000) 0, rgba(236,87,91,1.000000) 100.000000%);
  border-radius: 16rpx;
  height: 58rpx;
  display: flex;
  flex-direction: column;
  width: 174rpx;
}
.text_5-2 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 8rpx 0 0 60rpx;
}
.section_1 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 488rpx 0 30rpx 232rpx;
}