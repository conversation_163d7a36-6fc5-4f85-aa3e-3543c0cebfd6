
const apiService = require('../../utils/apiService');
const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    currentAddress: '定位中...',
    isLoading: true,
    locationFailed: false,
    weather: {
      temp: '--',
      weather: '--'
    },
    // 背景轮播图相关
    backgroundCarousels: [], // 背景轮播图数据
    currentBackgroundIndex: 0, // 当前背景图索引
    backgroundTimer: null, // 背景轮播定时器
    backgroundAutoPlay: true, // 是否自动播放背景轮播
    backgroundInterval: 5000, // 背景轮播间隔(毫秒)
    backgroundImage: '', // 默认背景图
    cityList: [
    ],
    selectedCityId: 0,
    provinces: [
    ],
    selectedProvinceIndex: 0,
    showProvinceModal: false,  // 省份选择弹窗是否显示

    // 轮播图相关数据
    currentCarouselIndex: 0,  // 当前轮播图索引
    carouselAutoPlay: true,   // 是否自动播放
    carouselInterval: 3000,   // 自动播放间隔(毫秒)
    carouselTimer: null,      // 轮播定时器

    // 省份对应的轮播图数据
    provinceCarouselData: {
    },

    // 当前省份的轮播图数据
    currentCarouselData: [],

    // 省份欢迎语数据
    provinceWelcomeMessages: {
      
    },

    // 当前欢迎语
    currentWelcomeMessage: '山水含福，福地江苏欢迎您~',

    // 省份对应的城市数据
    provinceCityData: {
     
    },

    // 城市对应的景区数据（改为数组，支持多个景区）
    cityScenicData: {
      
    },

    // 当前景区信息列表
    currentScenicList: [
      
    ],

    // 当前城市列表
    cityList: [
     
    ],

    // 当前选中的城市ID
    selectedCityId: 0
  },

  lifetimes: {
    attached: function() {
      // 使用节流优化初始化
      this.throttledInit();
    },

    detached: function() {
      // 清除所有定时器和监听器
      this.cleanup();
    },

    // 页面显示时恢复背景轮播
    show: function() {
      if (this.data.backgroundCarousels.length > 1) {
        this.startBackgroundAutoPlay();
      }
    },

    // 页面隐藏时暂停背景轮播
    hide: function() {
      this.stopBackgroundAutoPlay();
    }
  },

  methods: {
    // 性能优化：节流初始化
    throttledInit: function() {
      if (this.initTimer) {
        clearTimeout(this.initTimer);
      }
      this.initTimer = setTimeout(() => {
        this.loadInitialData();
      }, 100);
    },

    // 加载初始数据
    async loadInitialData() {
      try {
        console.log('开始加载初始数据...');

        // 并行加载省份数据、获取位置信息和背景轮播图
        const loadPromises = [
          this.loadProvincesData(),
          this.getCurrentLocation(),
          this.loadBackgroundCarousels()
        ];

        await Promise.allSettled(loadPromises);

        // 加载默认省份的相关数据
        const defaultProvince = this.data.provinces[0];
        if (defaultProvince) {
          await this.loadProvinceRelatedData(defaultProvince.id, defaultProvince.name);
        }

        console.log('初始数据加载完成');
      } catch (error) {
        console.error('初始数据加载失败:', error);
        apiService.handleError(error);
      }
    },

    // 加载省份数据
    async loadProvincesData() {
      try {
        const provinces = await apiService.getProvinces();

        // 处理省份数据，设置第一个为选中状态
        const processedProvinces = provinces.map((province, index) => ({
          ...province,
          selected: index === 0
        }));

        this.setData({
          provinces: processedProvinces
        });

        console.log('省份数据加载成功:', processedProvinces.length, '个省份');
        return processedProvinces;
      } catch (error) {
        console.error('加载省份数据失败:', error);
        // 使用默认数据
        const defaultProvinces = [
          { id: 1, name: '江苏', code: 'JS', selected: true },
          { id: 2, name: '浙江', code: 'ZJ', selected: false },
          { id: 3, name: '上海', code: 'SH', selected: false },
          { id: 4, name: '北京', code: 'BJ', selected: false },
          { id: 5, name: '广东', code: 'GD', selected: false }
        ];

        this.setData({
          provinces: defaultProvinces
        });

        throw error;
      }
    },

    // 加载省份相关数据
    async loadProvinceRelatedData(provinceId, provinceName) {
      try {
        console.log('加载省份相关数据:', provinceName);

        // 先清除旧的轮播图数据，防止显示旧数据
        this.stopCarouselAutoPlay();
        this.setData({
          currentCarouselData: [],
          currentCarouselIndex: 0,
          currentScenicList: []
        });

        // 并行加载城市、轮播图和景区数据
        const [citiesResult, carouselsResult, scenicsResult] = await apiService.batchRequest([
          apiService.getCities(provinceId),
          apiService.getCarousels(provinceId),
          apiService.getScenicsByProvince(provinceId, { limit: 20 })
        ]);

        // 处理城市数据
        if (citiesResult.success) {
          // 使用cityService的processCitiesData方法处理数据
          const cities = apiService.cityService.processCitiesData(citiesResult.data, true);
          this.setData({
            cityList: cities,
            selectedCityId: 0
          });
          console.log('城市数据加载成功:', citiesResult.data.length, '个城市');
        } else {
          console.error('城市数据加载失败:', citiesResult.error);
          // 设置默认的"全部"选项
          this.setData({
            cityList: [{ id: 0, name: '全部', code: 'ALL' }],
            selectedCityId: 0
          });
        }

        // 处理轮播图数据
        if (carouselsResult.success && carouselsResult.data.length > 0) {
          this.setData({
            currentCarouselData: carouselsResult.data,
            currentCarouselIndex: 0
          });
          this.startCarouselAutoPlay();
          console.log('轮播图数据加载成功:', carouselsResult.data.length, '张图片');
        } else {
          console.error('轮播图数据加载失败:', carouselsResult.error);
          this.setData({
            currentCarouselData: [],
            currentCarouselIndex: 0
          });
        }

        // 处理景区数据
        if (scenicsResult.success) {
          const scenicData = scenicsResult.data;
          // 处理分页数据结构
          let scenicList = [];
          if (scenicData.records && Array.isArray(scenicData.records)) {
            scenicList = scenicData.records;
          } else if (scenicData.list && Array.isArray(scenicData.list)) {
            scenicList = scenicData.list;
          } else if (Array.isArray(scenicData)) {
            scenicList = scenicData;
          }

          this.setData({
            currentScenicList: scenicList
          });
          console.log('景区数据加载成功:', scenicList.length, '个景区');
        } else {
          console.error('景区数据加载失败:', scenicsResult.error);
          this.setData({
            currentScenicList: []
          });
        }

      } catch (error) {
        console.error('加载省份相关数据失败:', error);
        apiService.handleError(error);
      }
    },

    // 加载背景轮播图
    async loadBackgroundCarousels() {
      try {
        console.log('开始加载背景轮播图...');
        const carousels = await apiService.getShouyeCarousels();

        if (carousels && carousels.length > 0) {
          this.setData({
            backgroundCarousels: carousels,
            currentBackgroundIndex: 0,
            backgroundImage: carousels[0].imageUrl || carousels[0].image
          });

          // 如果有多张图片，启动自动轮播
          if (carousels.length > 1) {
            this.startBackgroundAutoPlay();
          }

          console.log('背景轮播图加载成功:', carousels.length, '张图片');
        } else {
          console.log('未获取到背景轮播图，使用默认背景');
        }
      } catch (error) {
        console.error('加载背景轮播图失败:', error);
        // 保持默认背景图片
      }
    },

    // 启动背景自动播放
    startBackgroundAutoPlay: function() {
      this.stopBackgroundAutoPlay(); // 先清除现有定时器

      if (this.data.backgroundAutoPlay && this.data.backgroundCarousels.length > 1) {
        const timer = setInterval(() => {
          this.nextBackgroundImage();
        }, this.data.backgroundInterval);

        this.setData({
          backgroundTimer: timer
        });
      }
    },

    // 停止背景自动播放
    stopBackgroundAutoPlay: function() {
      if (this.data.backgroundTimer) {
        clearInterval(this.data.backgroundTimer);
        this.setData({
          backgroundTimer: null
        });
      }
    },

    // 切换到下一张背景图
    nextBackgroundImage: function() {
      const carousels = this.data.backgroundCarousels;
      if (carousels.length <= 1) return;

      const nextIndex = (this.data.currentBackgroundIndex + 1) % carousels.length;
      const nextImage = carousels[nextIndex];

      this.setData({
        currentBackgroundIndex: nextIndex,
        backgroundImage: nextImage.imageUrl || nextImage.image
      });
    },

    // 清理资源
    cleanup: function() {
      // 清除轮播定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }
      // 清除背景轮播定时器
      if (this.data.backgroundTimer) {
        clearInterval(this.data.backgroundTimer);
      }
      // 清除初始化定时器
      if (this.initTimer) {
        clearTimeout(this.initTimer);
      }
      // 清除防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
    },

    // 防抖函数
    debounce: function(func, wait) {
      return (...args) => {
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
        }
        this.debounceTimer = setTimeout(() => {
          func.apply(this, args);
        }, wait);
      };
    },
    getCurrentLocation: function() {
      this.setData({ isLoading: true, locationFailed: false });
      
      wx.getLocation({
        type: 'gcj02',
        success: res => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          this.getWeatherInfo(latitude, longitude);
          this.getAddressFromCoords(latitude, longitude)
            .then(result => {
              // 从返回的结果中提取需要的地址信息
              console.log(result)
              const address = result.address_component.province;
              this.setData({
                currentAddress: address,
                isLoading: false
              });
            })
            .catch(error => {
              console.error('获取地址失败:', error);
              this.setData({
                currentAddress: '获取地址失败',
                isLoading: false,
                locationFailed: true
              });
              wx.showToast({
                title: '获取地址失败，请检查网络后重试',
                icon: 'none',
                duration: 2000
              });
            });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          this.setData({
            currentAddress: '获取位置失败',
            isLoading: false,
            locationFailed: true
          });
          // 判断是否是因为用户拒绝授权导致的失败
          if (err.errMsg.includes('auth deny')) {
            wx.showModal({
              title: '提示',
              content: '需要您的位置权限才能为您提供更好的服务，是否前往设置打开权限？',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        this.getCurrentLocation();
                      }
                    }
                  });
                }
              }
            });
          } else {
            wx.showToast({
              title: '获取位置失败，请检查网络后重试',
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    },

    getAddressFromCoords: function(latitude, longitude) {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://apis.map.qq.com/ws/geocoder/v1/',
          data: {
            location: `${latitude},${longitude}`,
            key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS',
            get_poi: 0
          },
          success: res => {
            if (res.data && res.data.status === 0) {
              resolve(res.data.result);
            } else {
              reject(new Error(res.data.message || '获取地址信息失败'));
            }
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },

    // 获取天气信息
    getWeatherInfo: function(latitude, longitude) {
      wx.request({
        url: 'https://apis.map.qq.com/ws/weather/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS'
        },
        success: res => {
          console.log('天气API响应:', res.data);
          if (res.data && res.data.status === 0 && res.data.result && res.data.result.realtime && res.data.result.realtime.length > 0) {
            const weatherInfo = res.data.result.realtime[0].infos;
            this.setData({
              weather: {
                temp: `${weatherInfo.temperature}℃`,
                weather: weatherInfo.weather
              }
            });
            console.log('天气信息获取成功:', weatherInfo);
          } else {
            console.error('获取天气失败:', res.data);
            this.setData({
              weather: {
                temp: '--℃',
                weather: '--'
              }
            });
          }
        },
        fail: err => {
          console.error('天气API请求失败:', err);
          this.setData({
            weather: {
              temp: '--℃',
              weather: '--'
            }
          });
        }
      });
    },

    // 重试获取位置
    retryGetLocation: function() {
      if (!this.data.isLoading) {
        this.getCurrentLocation();
      }
    },


    
    // 选择省份（优化版本，使用防抖）
    selectProvince: function(e) {
      const index = e.currentTarget.dataset.index;

      // 防抖处理，避免快速点击导致的性能问题
      this.debounce(() => {
        this.handleProvinceSelect(index);
      }, 200)();
    },

    // 处理省份选择逻辑
    handleProvinceSelect: async function(index) {
      const provinces = [...this.data.provinces];

      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };

        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;

        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;

        // 批量更新数据，减少setData调用次数
        this.setData({
          provinces,
          selectedProvinceIndex: 0
        });

        console.log('选中省份:', provinces[0].name);

        // 加载新省份的相关数据
        await this.loadProvinceRelatedData(provinces[0].id, provinces[0].name);
      }
    },

    // 打开省份选择弹窗
    toggleProvinceModal: function() {
      this.setData({
        showProvinceModal: true
      });
      console.log('打开省份选择弹窗');
    },

    // 关闭省份选择弹窗
    closeProvinceModal: function() {
      this.setData({
        showProvinceModal: false
      });
      console.log('关闭省份选择弹窗');
    },

    // 阻止事件冒泡
    stopPropagation: function() {
      // 阻止点击弹窗内容时关闭弹窗
    },

    // 从弹窗中选择省份
    selectProvinceFromModal: async function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];

      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };

        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;

        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;

        this.setData({
          provinces,
          selectedProvinceIndex: 0,
          showProvinceModal: false  // 选择后关闭弹窗
        });

        console.log('从弹窗选中省份:', provinces[0].name);

        // 加载新省份的相关数据
        await this.loadProvinceRelatedData(provinces[0].id, provinces[0].name);
      } else {
        // 如果点击的是第一个省份，只关闭弹窗
        this.setData({
          showProvinceModal: false
        });
      }
    },



    // 开始自动播放
    startCarouselAutoPlay: function() {
      // 清除之前的定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }

      // 如果数据少于2个，不启动自动播放
      if (!this.data.carouselAutoPlay || this.data.currentCarouselData.length < 2) {
        return;
      }

      const timer = setInterval(() => {
        this.nextCarouselImage();
      }, this.data.carouselInterval);

      this.setData({
        carouselTimer: timer
      });
    },

    // 停止自动播放
    stopCarouselAutoPlay: function() {
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
        this.setData({
          carouselTimer: null
        });
      }
    },

    // 下一张图片
    nextCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const nextIndex = (currentIndex + 1) % totalImages;
      this.setData({
        currentCarouselIndex: nextIndex
      });
    },

    // 上一张图片
    prevCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const prevIndex = currentIndex === 0 ? totalImages - 1 : currentIndex - 1;
      this.setData({
        currentCarouselIndex: prevIndex
      });
    },

    // 轮播图点击事件
    onCarouselImageTap: async function(e) {
      const index = e.currentTarget.dataset.index || this.data.currentCarouselIndex;
      const imageData = this.data.currentCarouselData[index];

      if (!imageData) return;

      console.log('点击轮播图:', imageData);

      // 如果有关联的景区ID，跳转到景区详情页
      if (imageData.scenic_id) {
        try {
          // 先获取景区详情数据（可选，用于验证景区是否存在）
          // const scenicDetail = await apiService.getScenicDetail(imageData.scenic_id);

          const currentProvince = this.data.provinces[0].name;
          wx.navigateTo({
            url: `/pages/lanhu_jingquxiangqing/component?scenicId=${imageData.scenic_id}&province=${currentProvince}&title=${encodeURIComponent(imageData.title)}`,
            success: () => {
              console.log('跳转到景区详情页面:', imageData);
            },
            fail: (err) => {
              console.error('跳转失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } catch (error) {
          console.error('处理轮播图点击失败:', error);
          wx.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      } else {
        // 如果没有关联景区，显示轮播图信息
        wx.showToast({
          title: imageData.title || '查看详情',
          icon: 'none'
        });
      }
    },

    // 轮播图滑动事件
    onCarouselChange: function(e) {
      const current = e.detail.current;
      this.setData({
        currentCarouselIndex: current
      });

      // 重新启动自动播放
      this.startCarouselAutoPlay();
    },

    // 轮播图触摸开始
    onCarouselTouchStart: function() {
      this.stopCarouselAutoPlay();
    },

    // 轮播图触摸结束
    onCarouselTouchEnd: function() {
      this.startCarouselAutoPlay();
    },



    // 更新城市选择方法
    selectCity: async function(e) {
      const cityId = e.currentTarget.dataset.cityId;
      this.setData({
        selectedCityId: cityId
      });

      // 获取选中的城市信息
      const selectedCity = this.data.cityList.find(city => city.id === cityId);
      console.log('选中城市:', selectedCity.name);

      // 根据选中的城市更新景区数据
      await this.updateScenicDataByCity(cityId, selectedCity.name);
    },

    // 根据城市更新景区数据
    async updateScenicDataByCity(cityId, cityName) {
      try {
        const provinceId = this.data.provinces[0].id;
        let scenicsData;

        // 根据城市ID选择不同的API
        if (cityId === 0) {
          // 获取省份下的全部景区
          scenicsData = await apiService.getScenicsByProvince(provinceId, { limit: 20 });
        } else {
          // 获取指定城市的景区
          scenicsData = await apiService.getScenicsByCity(cityId, { limit: 20 });
        }

        // 处理分页数据结构
        let scenicList = [];
        if (scenicsData.records && Array.isArray(scenicsData.records)) {
          scenicList = scenicsData.records;
        } else if (scenicsData.list && Array.isArray(scenicsData.list)) {
          scenicList = scenicsData.list;
        } else if (Array.isArray(scenicsData)) {
          scenicList = scenicsData;
        }

        this.setData({
          currentScenicList: scenicList
        });

        console.log(`${cityName}景区数据加载成功:`, scenicList.length, '个景区');

        // 显示成功提示
        if (cityId !== 0) {
          wx.showToast({
            title: `已切换到${cityName}`,
            icon: 'success',
            duration: 1000
          });
        }

      } catch (error) {
        console.error('更新景区数据失败:', error);
        apiService.handleError(error);
        // 设置空数据，避免显示错误的景区信息
        this.setData({
          currentScenicList: []
        });
      }
    },

    // "更多"按钮点击事件
    onMoreButtonTap: function() {
      const currentProvince = this.data.provinces[0].name;
      const currentCity = this.data.cityList.find(city => city.id === this.data.selectedCityId);

      // 跳转到更多页面，传递省份和城市参数
      wx.navigateTo({
        url: `/pages/lanhu_gengduo/component?province=${currentProvince}&city=${currentCity ? currentCity.name : '全部'}`,
        success: () => {
          console.log('跳转到更多页面:', { province: currentProvince, city: currentCity?.name });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 景区卡片点击事件
    onScenicCardTap: async function(e) {
      const scenicId = e.currentTarget.dataset.scenicId;
      const scenicInfo = this.data.currentScenicList.find(item =>
        (item.scenic_id === scenicId) || (item.scenicId === scenicId) || (item.id === scenicId)
      );

      if (!scenicInfo) {
        console.error('未找到景区信息:', scenicId);
        wx.showToast({
          title: '景区信息不存在',
          icon: 'none'
        });
        return;
      }

      console.log('点击景区卡片:', scenicInfo);

      try {
        // 获取实际的景区ID
        const actualScenicId = scenicInfo.scenicId || scenicInfo.scenic_id || scenicInfo.id;

        if (!actualScenicId) {
          throw new Error('景区ID不存在');
        }

        // 跳转到景区详情页面
        wx.navigateTo({
          url: `/pages/lanhu_dulijingqu/component?scenicId=${actualScenicId}`,
          success: () => {
            console.log('跳转到景区详情页面:', {
              scenicId: actualScenicId,
              title: scenicInfo.title
            });
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('处理景区卡片点击失败:', error);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 图片加载错误处理
    onImageError: function(e) {
      console.error('图片加载失败:', e.detail);
      // 可以设置默认图片或进行其他处理
    },

    // 图片加载成功处理
    onImageLoad: function(e) {
      console.log('图片加载成功:', e.detail);
    },

    // 背景图片点击事件
    onBackgroundImageTap: function() {
      const currentCarousel = this.data.backgroundCarousels[this.data.currentBackgroundIndex];
      if (currentCarousel) {
        console.log('背景图片点击:', currentCarousel);
        // 可以在这里添加背景图片点击的处理逻辑
        // 比如显示图片信息、跳转到相关页面等
      }
    },

    // 手动切换背景图片（可用于手势操作）
    switchBackgroundImage: function() {
      if (this.data.backgroundCarousels.length > 1) {
        this.nextBackgroundImage();
        // 重新启动自动播放
        this.startBackgroundAutoPlay();
      }
    },


  }
});