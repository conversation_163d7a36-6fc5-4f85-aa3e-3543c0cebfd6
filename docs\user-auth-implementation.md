# 用户登录授权流程实现指南

## 1. 整体流程设计

### 1.1 登录流程图
```
用户点击"我的"页面
    ↓
检测登录状态
    ↓
未登录 → 显示登录确认弹窗 → 用户确认 → 微信登录 → 登录成功 → 显示授权提示
    ↓                                                    ↓
已登录 → 直接进入个人中心                                  用户授权 → 获取用户信息 → 完成
```

### 1.2 技术架构
- **前端**: 微信小程序
- **后端**: Node.js + Express
- **数据库**: MySQL
- **认证**: JWT Token
- **存储**: 微信小程序本地存储

## 2. 核心功能实现

### 2.1 用户服务模块 (utils/userService.js)

已实现的核心功能：
- 登录状态检测
- 微信登录
- 用户信息授权
- 手机号授权
- 用户信息更新
- 退出登录

### 2.2 "我的"页面实现 (pages/lanhu_wode/component.js)

已实现的核心功能：
- 登录状态显示
- 登录流程处理
- 授权流程处理
- 用户信息展示
- 退出登录

## 3. 后端接口实现

### 3.1 微信登录接口

```javascript
// routes/auth.js
const express = require('express');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const router = express.Router();

// 微信登录接口
router.post('/wechat/login', async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({
        code: 400,
        message: 'code is required'
      });
    }
    
    // 1. 向微信服务器获取 openid 和 session_key
    const wechatResponse = await axios.get('https://api.weixin.qq.com/sns/jscode2session', {
      params: {
        appid: process.env.WECHAT_APPID,
        secret: process.env.WECHAT_SECRET,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });
    
    if (wechatResponse.data.errcode) {
      return res.status(400).json({
        code: 400,
        message: wechatResponse.data.errmsg
      });
    }
    
    const { openid, session_key, unionid } = wechatResponse.data;
    
    // 2. 查询或创建用户
    let user = await db.query('SELECT * FROM users WHERE openid = ?', [openid]);
    
    if (user.length === 0) {
      // 创建新用户
      const result = await db.query(`
        INSERT INTO users (openid, unionid, status, created_at, updated_at) 
        VALUES (?, ?, 1, NOW(), NOW())
      `, [openid, unionid]);
      
      user = await db.query('SELECT * FROM users WHERE id = ?', [result.insertId]);
    } else {
      user = user[0];
    }
    
    // 3. 生成 JWT Token
    const token = jwt.sign(
      { 
        userId: user.id, 
        openid: user.openid 
      },
      process.env.JWT_SECRET,
      { expiresIn: '30d' }
    );
    
    // 4. 返回登录结果
    res.json({
      code: 200,
      message: 'success',
      data: {
        token,
        user_info: {
          id: user.id,
          openid: user.openid,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          province: user.province,
          city: user.city,
          created_at: user.created_at
        }
      }
    });
    
  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败，请重试'
    });
  }
});

module.exports = router;
```

### 3.2 用户信息接口

```javascript
// routes/user.js
const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const router = express.Router();

// JWT 验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      code: 401,
      message: 'Access token required'
    });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        code: 403,
        message: 'Invalid token'
      });
    }
    req.user = user;
    next();
  });
};

// 获取用户信息
router.get('/info', authenticateToken, async (req, res) => {
  try {
    const user = await db.query('SELECT * FROM users WHERE id = ?', [req.user.userId]);
    
    if (user.length === 0) {
      return res.status(404).json({
        code: 404,
        message: 'User not found'
      });
    }
    
    res.json({
      code: 200,
      message: 'success',
      data: {
        id: user[0].id,
        openid: user[0].openid,
        nickname: user[0].nickname,
        avatar: user[0].avatar,
        phone: user[0].phone,
        province: user[0].province,
        city: user[0].city,
        created_at: user[0].created_at
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: error.message
    });
  }
});

// 更新用户信息
router.put('/info', authenticateToken, async (req, res) => {
  try {
    const { nickname, avatar, phone, province, city } = req.body;
    
    const updateFields = [];
    const updateValues = [];
    
    if (nickname) {
      updateFields.push('nickname = ?');
      updateValues.push(nickname);
    }
    
    if (avatar) {
      updateFields.push('avatar = ?');
      updateValues.push(avatar);
    }
    
    if (phone) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    
    if (province) {
      updateFields.push('province = ?');
      updateValues.push(province);
    }
    
    if (city) {
      updateFields.push('city = ?');
      updateValues.push(city);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({
        code: 400,
        message: 'No fields to update'
      });
    }
    
    updateFields.push('updated_at = NOW()');
    updateValues.push(req.user.userId);
    
    await db.query(`
      UPDATE users 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `, updateValues);
    
    // 返回更新后的用户信息
    const updatedUser = await db.query('SELECT * FROM users WHERE id = ?', [req.user.userId]);
    
    res.json({
      code: 200,
      message: 'success',
      data: {
        id: updatedUser[0].id,
        openid: updatedUser[0].openid,
        nickname: updatedUser[0].nickname,
        avatar: updatedUser[0].avatar,
        phone: updatedUser[0].phone,
        province: updatedUser[0].province,
        city: updatedUser[0].city,
        updated_at: updatedUser[0].updated_at
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: error.message
    });
  }
});

// 解密手机号
router.post('/decrypt-phone', authenticateToken, async (req, res) => {
  try {
    const { encrypted_data, iv } = req.body;
    
    if (!encrypted_data || !iv) {
      return res.status(400).json({
        code: 400,
        message: 'encrypted_data and iv are required'
      });
    }
    
    // 这里需要使用用户的 session_key 来解密
    // 实际项目中需要存储和管理 session_key
    const sessionKey = await getSessionKey(req.user.openid);
    
    if (!sessionKey) {
      return res.status(400).json({
        code: 400,
        message: 'Session key not found'
      });
    }
    
    // 解密手机号
    const decryptedData = decryptWechatData(encrypted_data, iv, sessionKey);
    
    if (!decryptedData || !decryptedData.phoneNumber) {
      return res.status(400).json({
        code: 400,
        message: 'Failed to decrypt phone number'
      });
    }
    
    res.json({
      code: 200,
      message: 'success',
      data: {
        phoneNumber: decryptedData.phoneNumber,
        purePhoneNumber: decryptedData.purePhoneNumber,
        countryCode: decryptedData.countryCode
      }
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: error.message
    });
  }
});

// 微信数据解密函数
function decryptWechatData(encryptedData, iv, sessionKey) {
  try {
    const decipher = crypto.createDecipheriv('aes-128-cbc', 
      Buffer.from(sessionKey, 'base64'), 
      Buffer.from(iv, 'base64')
    );
    
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('解密失败:', error);
    return null;
  }
}

module.exports = router;
```

## 4. 环境配置

### 4.1 环境变量 (.env)

```env
# 微信小程序配置
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret

# JWT 配置
JWT_SECRET=your_jwt_secret_key

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=tourism_guide

# 服务器配置
PORT=3000
NODE_ENV=development
```

### 4.2 小程序配置 (app.json)

```json
{
  "permission": {
    "scope.userInfo": {
      "desc": "用于完善用户资料"
    },
    "scope.userLocation": {
      "desc": "用于获取当前位置，推荐附近景区"
    }
  },
  "requiredPrivateInfos": [
    "getLocation",
    "chooseLocation"
  ]
}
```

## 5. 错误处理和异常情况

### 5.1 常见错误处理

```javascript
// 网络错误处理
const handleNetworkError = (error) => {
  if (error.message.includes('timeout')) {
    wx.showToast({
      title: '网络超时，请重试',
      icon: 'none'
    });
  } else if (error.message.includes('network')) {
    wx.showToast({
      title: '网络连接失败',
      icon: 'none'
    });
  } else {
    wx.showToast({
      title: '服务异常，请稍后重试',
      icon: 'none'
    });
  }
};

// 授权失败处理
const handleAuthError = (error) => {
  if (error.message.includes('deny')) {
    wx.showModal({
      title: '授权提示',
      content: '需要您的授权才能使用相关功能，请前往设置页面开启授权',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting();
        }
      }
    });
  } else {
    wx.showToast({
      title: '授权失败，请重试',
      icon: 'none'
    });
  }
};
```

### 5.2 登录状态维护

```javascript
// 定期检查登录状态
const checkLoginStatus = () => {
  const token = wx.getStorageSync('user_token');
  if (!token) {
    return false;
  }
  
  // 检查 token 是否过期
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    
    if (payload.exp < currentTime) {
      // token 已过期，清除本地存储
      wx.removeStorageSync('user_token');
      wx.removeStorageSync('user_info');
      return false;
    }
    
    return true;
  } catch (error) {
    // token 格式错误，清除本地存储
    wx.removeStorageSync('user_token');
    wx.removeStorageSync('user_info');
    return false;
  }
};
```

## 6. 安全考虑

### 6.1 数据安全
- 敏感信息加密存储
- HTTPS 传输
- JWT Token 定期刷新
- 输入数据验证和过滤

### 6.2 隐私保护
- 最小化权限申请
- 明确告知用户数据用途
- 提供数据删除功能
- 遵循相关法律法规

## 7. 测试验证

### 7.1 功能测试
- 登录流程测试
- 授权流程测试
- 退出登录测试
- 异常情况测试

### 7.2 兼容性测试
- 不同微信版本
- 不同手机型号
- 网络异常情况
- 权限拒绝情况

通过以上实现，可以完成完整的用户登录授权功能，为用户提供安全、便捷的登录体验。
