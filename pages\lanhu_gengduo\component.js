Component({
  properties: {},
  data: {
    province: '',
    city: '',
    scenicList: []
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("更多页面加载");
      this.getPageParams();
    },
    detached: function () {
      console.info("更多页面卸载");
    },
  },
  methods: {
    // 获取页面参数
    getPageParams: function() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      
      console.log('更多页面接收到的参数:', options);
      
      this.setData({
        province: options.province || '',
        city: options.city || ''
      });
      
      // 根据参数加载景区列表
      this.loadScenicList();
    },
    
    // 加载景区列表
    loadScenicList: function() {
      // 这里可以根据省份和城市从服务器获取景区列表
      // 目前使用模拟数据
      const mockScenicList = [
        {
          id: 1,
          name: `${this.data.city}热门景区1`,
          description: '精彩景区介绍...',
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png'
        },
        {
          id: 2,
          name: `${this.data.city}热门景区2`,
          description: '精彩景区介绍...',
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png'
        }
      ];
      
      this.setData({
        scenicList: mockScenicList
      });
      
      console.log('景区列表加载完成:', mockScenicList);
    }
  },
});
