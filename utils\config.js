// 应用配置文件
const config = {
  // API配置
  api: {
    baseUrl: 'http://localhost:8080', // 替换为实际的API域名
    timeout: 10000
  },

  // 缓存配置
  cache: {
    // 省份数据缓存时间（毫秒）
    provincesCache: 10 * 60 * 1000, // 10分钟
    // 城市数据缓存时间
    citiesCache: 5 * 60 * 1000, // 5分钟
    // 轮播图缓存时间
    carouselsCache: 3 * 60 * 1000, // 3分钟
    // 景区数据缓存时间
    scenicsCache: 2 * 60 * 1000, // 2分钟
  },

  // 分页配置
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 50
  },

  // 图片配置
  image: {
    // 默认图片
    defaultAvatar: '/images/default-avatar.png',
    defaultScenic: '/images/default-scenic.png',
    defaultCarousel: '/images/default-carousel.png',
    // 图片质量
    quality: 80,
    // 图片压缩
    compress: true
  },

  // 地图配置
  map: {
    // 默认中心点（南京）
    defaultCenter: {
      latitude: 32.0603,
      longitude: 118.7969
    },
    // 默认缩放级别
    defaultZoom: 11
  },

  // 用户配置
  user: {
    // Token存储key
    tokenKey: 'user_token',
    // 用户信息存储key
    userInfoKey: 'user_info',
    // Token过期时间（天）
    tokenExpireDays: 30
  },

  // 错误码配置
  errorCodes: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500,
    NETWORK_ERROR: -1,
    TIMEOUT_ERROR: -2
  },

  // 错误消息配置
  errorMessages: {
    [200]: '操作成功',
    [400]: '请求参数错误',
    [401]: '未授权，请先登录',
    [403]: '权限不足',
    [404]: '资源不存在',
    [500]: '服务器内部错误',
    [-1]: '网络连接失败',
    [-2]: '请求超时'
  },

  // 获取错误消息
  getErrorMessage(code) {
    return this.errorMessages[code] || '未知错误';
  },

  // 小程序配置
  miniProgram: {
    // 页面路径
    pages: {
      home: '/pages/lanhu_shouye/component',
      scenic: '/pages/lanhu_jingquxiangqing/component',
      profile: '/pages/lanhu_wode/component'
    },
    // 分享配置
    share: {
      title: '旅游讲解小程序',
      desc: '发现美好旅程，专业讲解服务',
      imageUrl: '/images/share-logo.png'
    }
  }
};

module.exports = config;
