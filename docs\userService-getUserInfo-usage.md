# userService.getUserInfo() 方法使用说明

## 方法概述

`getUserInfo(userId)` 方法用于根据用户ID从服务器获取用户的详细信息。

## API 接口

- **接口地址**: `/api/user/{userId}`
- **请求方式**: `GET`
- **需要认证**: 是（需要 Bearer Token）

## 方法签名

```javascript
async getUserInfo(userId)
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | number | 是 | 用户ID |

## 返回值

成功时返回用户信息对象：

```javascript
{
  id: 123,                    // 用户ID
  nickname: "用户昵称",        // 用户昵称
  avatar: "头像URL",          // 头像URL
  phone: "手机号码",          // 手机号码
  province: "省份",           // 省份
  city: "城市",              // 城市
  openid: "微信openid",       // 微信openid
  unionid: "微信unionid",     // 微信unionid
  status: 1,                 // 状态：1-正常，0-禁用
  createdAt: "2023-01-01T00:00:00Z",  // 创建时间
  updatedAt: "2023-01-01T00:00:00Z"   // 更新时间
}
```

## 使用示例

### 基本使用

```javascript
const userService = require('../utils/userService');

// 获取用户信息
async function loadUserInfo() {
  try {
    const userId = 123;
    const userInfo = await userService.getUserInfo(userId);
    
    console.log('用户信息:', userInfo);
    
    // 使用用户信息更新页面
    this.setData({
      userInfo: userInfo
    });
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    wx.showToast({
      title: error.message,
      icon: 'none'
    });
  }
}
```

### 在页面中使用

```javascript
// pages/profile/profile.js
const userService = require('../../utils/userService');

Page({
  data: {
    userInfo: null,
    loading: true
  },

  onLoad(options) {
    const userId = options.userId || this.getCurrentUserId();
    this.loadUserInfo(userId);
  },

  async loadUserInfo(userId) {
    try {
      this.setData({ loading: true });
      
      const userInfo = await userService.getUserInfo(userId);
      
      this.setData({
        userInfo: userInfo,
        loading: false
      });
      
    } catch (error) {
      console.error('加载用户信息失败:', error);
      
      this.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  getCurrentUserId() {
    // 从本地存储或其他地方获取当前用户ID
    const loginStatus = userService.checkLoginStatus();
    return loginStatus.userInfo?.id;
  }
});
```

### 错误处理

```javascript
async function handleGetUserInfo(userId) {
  try {
    const userInfo = await userService.getUserInfo(userId);
    return userInfo;
    
  } catch (error) {
    // 根据不同错误类型进行处理
    if (error.message.includes('用户未登录')) {
      // 跳转到登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } else if (error.message.includes('用户不存在')) {
      wx.showToast({
        title: '用户不存在',
        icon: 'none'
      });
    } else if (error.message.includes('网络请求失败')) {
      wx.showToast({
        title: '网络连接失败，请检查网络',
        icon: 'none'
      });
    } else {
      wx.showToast({
        title: error.message || '获取用户信息失败',
        icon: 'none'
      });
    }
    
    throw error;
  }
}
```

## 错误类型

| 错误信息 | 说明 | 处理建议 |
|----------|------|----------|
| "用户ID不能为空" | 未传入userId参数 | 检查参数传递 |
| "用户未登录" | 本地没有有效的token | 引导用户登录 |
| "用户未授权，请重新登录" | token已过期或无效 | 清除本地登录信息，重新登录 |
| "用户不存在" | 指定的用户ID不存在 | 检查用户ID是否正确 |
| "网络请求失败" | 网络连接问题 | 检查网络连接，重试请求 |

## 注意事项

1. **认证要求**: 此方法需要用户已登录，会自动携带存储的 token
2. **参数验证**: userId 参数必须提供且不能为空
3. **错误处理**: 建议使用 try-catch 包装调用，并根据错误类型进行相应处理
4. **缓存策略**: 此方法每次都会从服务器获取最新数据，如需缓存可结合 `getLocalUserInfo()` 方法使用

## 相关方法

- `getLocalUserInfo()`: 获取本地存储的用户信息
- `checkLoginStatus()`: 检查用户登录状态
- `updateUserInfo(userInfo)`: 更新用户信息
