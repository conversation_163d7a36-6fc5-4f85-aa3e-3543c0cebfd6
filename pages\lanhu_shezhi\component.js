const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    userInfo: null,
    isLoading: false,
    tempAvatarUrl: '', // 临时头像URL
    tempNickname: ''   // 临时昵称
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("设置页面加载");
      this.loadUserInfo();
    },
    detached: function () {
      console.info("设置页面卸载");
    },
  },
  methods: {
    // 加载用户信息
    loadUserInfo: function() {
      const loginStatus = userService.checkLoginStatus();
      if (loginStatus.isLoggedIn) {
        this.setData({
          userInfo: loginStatus.userInfo,
          tempNickname: loginStatus.userInfo.nickname || ''
        });
      }
    },

    // 头像选择
    onChooseAvatar: async function(e) {
      try {
        const avatarUrl = e.detail.avatarUrl;
        console.log('选择的头像:', avatarUrl);

        this.setData({
          tempAvatarUrl: avatarUrl,
          isLoading: true
        });

        wx.showLoading({ title: '更新头像中...' });

        // 上传头像并更新用户信息
        await userService.updateUserInfo({
          avatar: avatarUrl
        });

        // 更新本地显示
        const updatedUserInfo = {
          ...this.data.userInfo,
          avatar: avatarUrl
        };

        this.setData({
          userInfo: updatedUserInfo,
          tempAvatarUrl: '',
          isLoading: false
        });

        wx.hideLoading();
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });

      } catch (error) {
        console.error('头像更新失败:', error);
        this.setData({
          tempAvatarUrl: '',
          isLoading: false
        });
        wx.hideLoading();
        wx.showToast({
          title: error.message || '头像更新失败',
          icon: 'none'
        });
      }
    },

    // 昵称输入
    onNicknameInput: function(e) {
      this.setData({
        tempNickname: e.detail.value
      });
    },

    // 昵称失去焦点时更新
    onNicknameBlur: async function() {
      const newNickname = this.data.tempNickname.trim();
      const currentNickname = this.data.userInfo?.nickname || '';

      // 如果昵称没有变化，不进行更新
      if (newNickname === currentNickname || !newNickname) {
        this.setData({
          tempNickname: currentNickname
        });
        return;
      }

      try {
        this.setData({ isLoading: true });
        wx.showLoading({ title: '更新昵称中...' });

        // 更新用户信息
        await userService.updateUserInfo({
          nickname: newNickname
        });

        // 更新本地显示
        const updatedUserInfo = {
          ...this.data.userInfo,
          nickname: newNickname
        };

        this.setData({
          userInfo: updatedUserInfo,
          isLoading: false
        });

        wx.hideLoading();
        wx.showToast({
          title: '昵称更新成功',
          icon: 'success'
        });

      } catch (error) {
        console.error('昵称更新失败:', error);
        this.setData({
          tempNickname: currentNickname,
          isLoading: false
        });
        wx.hideLoading();
        wx.showToast({
          title: error.message || '昵称更新失败',
          icon: 'none'
        });
      }
    },

    // 关于我们点击事件
    onAboutUsClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_guizeshuoming/component?type=about'
      });
    },

    // 用户协议点击事件
    onUserAgreementClick: function() {
      wx.navigateTo({
        url: '/pages/lanhu_guizeshuoming/component?type=agreement'
      });
    },

    // 退出登录
    onLogout: function() {
      wx.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？退出后需要重新登录才能使用相关功能。',
        confirmText: '确定退出',
        cancelText: '取消',
        confirmColor: '#ff4757',
        success: (res) => {
          if (res.confirm) {
            this.performLogout();
          }
        }
      });
    },

    // 执行退出登录
    performLogout: function() {
      try {
        // 清除用户登录状态和缓存数据
        const success = userService.logout();

        if (success) {
          // 清除页面数据
          this.setData({
            userInfo: null,
            tempNickname: '',
            tempAvatarUrl: ''
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 1500
          });

          // 延迟跳转，让用户看到提示
          setTimeout(() => {
            // 跳转到首页并清除页面栈
            wx.reLaunch({
              url: '/pages/lanhu_shouye/component'
            });
          }, 1500);
        } else {
          wx.showToast({
            title: '退出登录失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('退出登录失败:', error);
        wx.showToast({
          title: '退出登录失败',
          icon: 'none'
        });
      }
    }
  },
});
