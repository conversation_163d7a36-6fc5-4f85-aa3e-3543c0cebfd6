.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.block_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 264rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_1 {
  width: 688rpx;
  height: 128rpx;
  flex-direction: row;
  display: flex;
  margin: 48rpx 0 0 30rpx;
}
.image-text_1 {
  width: 56rpx;
  height: 116rpx;
  margin-top: 12rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-left: 8rpx;
}
.text-group_1 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 42rpx;
}
.text_1 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 74rpx;
}
.text-wrapper_1 {
  width: 128rpx;
  height: 128rpx;
  margin-left: 68rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_2 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_3 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 44rpx 0 0 6rpx;
}
.text_4 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 36rpx;
}
.text_5 {
  width: 84rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 88rpx 0 0 74rpx;
}
.box_2 {
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: row;
  margin: 12rpx 0 8rpx 34rpx;
}
.group_1 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 2rpx;
  width: 46rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
}
.block_2 {
  width: 750rpx;
  height: 1362rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.image_3 {
  width: 332rpx;
  height: 274rpx;
  margin: 250rpx 0 0 208rpx;
}
.group_2 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 804rpx 0 26rpx 232rpx;
}